#!/usr/bin/env python3
"""
Chess Game GUI using tkinter
A beautiful graphical interface for the chess game with AI
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import chess
from chess_game import evaluate_board, get_best_move
import threading
import time


class ChessGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Chess Game with AI")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        
        # Game state
        self.board = chess.Board()
        self.selected_square = None
        self.highlighted_squares = set()
        self.game_mode = "human_vs_ai"  # human_vs_ai, ai_vs_ai, human_vs_human
        self.human_color = chess.WHITE
        self.ai_depth = 3
        self.game_running = True
        self.ai_thinking = False
        self.last_move = None
        
        # Colors
        self.colors = {
            'light_square': '#f0d9b5',
            'dark_square': '#b58863',
            'highlight': '#ffff00',
            'selected': '#ff6b6b',
            'legal_move': '#90EE90',
            'last_move': '#ffd700'
        }
        
        # Chess piece Unicode symbols
        self.piece_symbols = {
            chess.PAWN: {'white': '♙', 'black': '♟'},
            chess.ROOK: {'white': '♖', 'black': '♜'},
            chess.KNIGHT: {'white': '♘', 'black': '♞'},
            chess.BISHOP: {'white': '♗', 'black': '♝'},
            chess.QUEEN: {'white': '♕', 'black': '♛'},
            chess.KING: {'white': '♔', 'black': '♚'}
        }
        
        self.setup_ui()
        self.update_board()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for board
        self.board_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Right panel for controls and info
        self.control_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        self.control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.setup_board()
        self.setup_controls()
        
    def setup_board(self):
        """Create the chess board."""
        # Board title
        title_label = tk.Label(self.board_frame, text="Chess Board", 
                              font=('Arial', 16, 'bold'), 
                              bg='#34495e', fg='white')
        title_label.pack(pady=10)
        
        # Create board canvas
        self.canvas = tk.Canvas(self.board_frame, width=480, height=480, 
                               bg='white', highlightthickness=0)
        self.canvas.pack(padx=20, pady=20)
        
        # Bind mouse events
        self.canvas.bind("<Button-1>", self.on_square_click)
        self.canvas.bind("<Motion>", self.on_mouse_motion)
        
        # Create squares
        self.squares = {}
        self.square_size = 60
        
        for rank in range(8):
            for file in range(8):
                x1 = file * self.square_size
                y1 = rank * self.square_size
                x2 = x1 + self.square_size
                y2 = y1 + self.square_size
                
                # Determine square color
                is_light = (rank + file) % 2 == 0
                color = self.colors['light_square'] if is_light else self.colors['dark_square']
                
                # Create square
                square = self.canvas.create_rectangle(x1, y1, x2, y2, 
                                                    fill=color, outline='black', width=1)
                
                # Store square reference
                chess_square = chess.square(file, 7 - rank)  # Convert to chess coordinates
                self.squares[chess_square] = {
                    'rect': square,
                    'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                    'piece_text': None
                }
        
        # Add coordinate labels
        self.add_coordinates()
        
    def add_coordinates(self):
        """Add file and rank labels to the board."""
        # File labels (a-h)
        for file in range(8):
            x = file * self.square_size + self.square_size // 2
            y = 8 * self.square_size + 10
            self.canvas.create_text(x, y, text=chr(ord('a') + file), 
                                  font=('Arial', 12, 'bold'), fill='black')
        
        # Rank labels (1-8)
        for rank in range(8):
            x = -15
            y = rank * self.square_size + self.square_size // 2
            self.canvas.create_text(x, y, text=str(8 - rank), 
                                  font=('Arial', 12, 'bold'), fill='black')
    
    def setup_controls(self):
        """Setup the control panel."""
        # Title
        title = tk.Label(self.control_frame, text="Chess Game Controls", 
                        font=('Arial', 16, 'bold'), bg='#34495e', fg='white')
        title.pack(pady=10)
        
        # Game mode selection
        mode_frame = tk.LabelFrame(self.control_frame, text="Game Mode", 
                                  font=('Arial', 12, 'bold'), 
                                  bg='#34495e', fg='white')
        mode_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.mode_var = tk.StringVar(value="human_vs_ai")
        modes = [("Human vs AI", "human_vs_ai"), 
                ("AI vs AI", "ai_vs_ai"), 
                ("Human vs Human", "human_vs_human")]
        
        for text, value in modes:
            rb = tk.Radiobutton(mode_frame, text=text, variable=self.mode_var, 
                               value=value, bg='#34495e', fg='white', 
                               selectcolor='#2c3e50', font=('Arial', 10),
                               command=self.on_mode_change)
            rb.pack(anchor=tk.W, padx=10, pady=2)
        
        # AI settings
        ai_frame = tk.LabelFrame(self.control_frame, text="AI Settings", 
                                font=('Arial', 12, 'bold'), 
                                bg='#34495e', fg='white')
        ai_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # AI difficulty
        tk.Label(ai_frame, text="AI Difficulty:", bg='#34495e', fg='white', 
                font=('Arial', 10)).pack(anchor=tk.W, padx=10, pady=2)
        
        self.difficulty_var = tk.IntVar(value=3)
        difficulty_scale = tk.Scale(ai_frame, from_=1, to=5, orient=tk.HORIZONTAL,
                                   variable=self.difficulty_var, bg='#34495e', 
                                   fg='white', highlightbackground='#34495e',
                                   command=self.on_difficulty_change)
        difficulty_scale.pack(fill=tk.X, padx=10, pady=2)
        
        # Human color selection (for human vs AI)
        color_frame = tk.Frame(ai_frame, bg='#34495e')
        color_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(color_frame, text="Play as:", bg='#34495e', fg='white', 
                font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.color_var = tk.StringVar(value="white")
        white_rb = tk.Radiobutton(color_frame, text="White", variable=self.color_var, 
                                 value="white", bg='#34495e', fg='white', 
                                 selectcolor='#2c3e50', font=('Arial', 10),
                                 command=self.on_color_change)
        white_rb.pack(side=tk.LEFT, padx=10)
        
        black_rb = tk.Radiobutton(color_frame, text="Black", variable=self.color_var, 
                                 value="black", bg='#34495e', fg='white', 
                                 selectcolor='#2c3e50', font=('Arial', 10),
                                 command=self.on_color_change)
        black_rb.pack(side=tk.LEFT, padx=10)
        
        # Game controls
        control_frame = tk.LabelFrame(self.control_frame, text="Game Controls", 
                                     font=('Arial', 12, 'bold'), 
                                     bg='#34495e', fg='white')
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        button_style = {'bg': '#3498db', 'fg': 'white', 'font': ('Arial', 10, 'bold'),
                       'relief': tk.RAISED, 'bd': 2, 'padx': 10, 'pady': 5}
        
        new_game_btn = tk.Button(control_frame, text="New Game", 
                                command=self.new_game, **button_style)
        new_game_btn.pack(fill=tk.X, padx=10, pady=2)
        
        self.ai_move_btn = tk.Button(control_frame, text="AI Move", 
                                    command=self.make_ai_move, **button_style)
        self.ai_move_btn.pack(fill=tk.X, padx=10, pady=2)
        
        undo_btn = tk.Button(control_frame, text="Undo Move", 
                            command=self.undo_move, **button_style)
        undo_btn.pack(fill=tk.X, padx=10, pady=2)
        
        # Game status
        status_frame = tk.LabelFrame(self.control_frame, text="Game Status", 
                                    font=('Arial', 12, 'bold'), 
                                    bg='#34495e', fg='white')
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.status_text = tk.Text(status_frame, height=8, width=30, 
                                  bg='#2c3e50', fg='white', font=('Arial', 10),
                                  wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Board evaluation
        eval_frame = tk.Frame(self.control_frame, bg='#34495e')
        eval_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(eval_frame, text="Position Evaluation:", bg='#34495e', fg='white', 
                font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        self.eval_label = tk.Label(eval_frame, text="0", bg='#2c3e50', fg='white', 
                                  font=('Arial', 12, 'bold'), relief=tk.SUNKEN, bd=2)
        self.eval_label.pack(fill=tk.X, pady=2)
        
        self.update_status("Game started! White to move.")

    def update_board(self):
        """Update the visual representation of the board."""
        # Clear existing pieces
        for square_info in self.squares.values():
            if square_info['piece_text']:
                self.canvas.delete(square_info['piece_text'])
                square_info['piece_text'] = None

        # Reset square colors
        self.reset_square_colors()

        # Highlight last move
        if self.last_move:
            self.highlight_square(self.last_move.from_square, self.colors['last_move'])
            self.highlight_square(self.last_move.to_square, self.colors['last_move'])

        # Draw pieces
        for square, piece in self.board.piece_map().items():
            self.draw_piece(square, piece)

        # Update evaluation
        evaluation = evaluate_board(self.board)
        self.eval_label.config(text=f"{evaluation:+d}")

        # Check game status
        self.check_game_status()

    def draw_piece(self, square, piece):
        """Draw a piece on the specified square."""
        square_info = self.squares[square]
        x = square_info['x1'] + self.square_size // 2
        y = square_info['y1'] + self.square_size // 2

        color = 'white' if piece.color == chess.WHITE else 'black'
        symbol = self.piece_symbols[piece.piece_type][color]

        piece_text = self.canvas.create_text(x, y, text=symbol,
                                           font=('Arial', 36),
                                           fill='black' if color == 'white' else 'white')
        square_info['piece_text'] = piece_text

    def reset_square_colors(self):
        """Reset all squares to their default colors."""
        for square, square_info in self.squares.items():
            file = chess.square_file(square)
            rank = chess.square_rank(square)
            is_light = (rank + file) % 2 == 0
            color = self.colors['light_square'] if is_light else self.colors['dark_square']
            self.canvas.itemconfig(square_info['rect'], fill=color)

    def highlight_square(self, square, color):
        """Highlight a specific square with the given color."""
        if square in self.squares:
            self.canvas.itemconfig(self.squares[square]['rect'], fill=color)

    def get_square_from_coords(self, x, y):
        """Get the chess square from canvas coordinates."""
        if 0 <= x < 8 * self.square_size and 0 <= y < 8 * self.square_size:
            file = x // self.square_size
            rank = 7 - (y // self.square_size)  # Flip rank
            return chess.square(file, rank)
        return None

    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if not self.game_running or self.ai_thinking:
            return

        square = self.get_square_from_coords(event.x, event.y)
        if square is None:
            return

        # If in AI vs AI mode, don't allow human moves
        if self.game_mode == "ai_vs_ai":
            return

        # If in human vs AI mode, check if it's human's turn
        if (self.game_mode == "human_vs_ai" and
            self.board.turn != self.human_color):
            return

        if self.selected_square is None:
            # Select a piece
            piece = self.board.piece_at(square)
            if piece and piece.color == self.board.turn:
                self.selected_square = square
                self.highlight_square(square, self.colors['selected'])
                self.show_legal_moves(square)
        else:
            # Try to make a move
            move = chess.Move(self.selected_square, square)

            # Check for promotion
            piece = self.board.piece_at(self.selected_square)
            if (piece and piece.piece_type == chess.PAWN and
                chess.square_rank(square) in [0, 7]):
                # For simplicity, always promote to queen
                move = chess.Move(self.selected_square, square, promotion=chess.QUEEN)

            if move in self.board.legal_moves:
                self.make_move(move)

            # Deselect
            self.selected_square = None
            self.update_board()

    def show_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlight_square(move.to_square, self.colors['legal_move'])

    def make_move(self, move):
        """Make a move on the board."""
        # Get move notation before making the move
        move_str = self.board.san(move)

        # Store the move for highlighting
        self.last_move = move

        # Make the move
        self.board.push(move)

        # Update status
        turn = "White" if self.board.turn == chess.WHITE else "Black"
        self.update_status(f"Move: {move_str}. {turn} to move.")

        # Update board display
        self.update_board()

        # If it's AI's turn in human vs AI mode, make AI move
        if (self.game_mode == "human_vs_ai" and
            self.board.turn != self.human_color and
            not self.board.is_game_over()):
            self.root.after(500, self.make_ai_move)  # Delay for better UX

    def make_ai_move(self):
        """Make an AI move."""
        if not self.game_running or self.board.is_game_over():
            return

        self.ai_thinking = True
        self.update_status("AI is thinking...")

        # Run AI calculation in a separate thread
        def ai_thread():
            try:
                best_move = get_best_move(self.board, depth=self.ai_depth)
                if best_move:
                    # Schedule the move to be made in the main thread
                    self.root.after(0, lambda: self.complete_ai_move(best_move))
                else:
                    self.root.after(0, lambda: self.update_status("AI couldn't find a move!"))
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"AI error: {str(e)}"))
            finally:
                self.ai_thinking = False

        threading.Thread(target=ai_thread, daemon=True).start()

    def complete_ai_move(self, move):
        """Complete the AI move in the main thread."""
        self.make_move(move)
        self.ai_thinking = False

        # If in AI vs AI mode, continue with next move
        if (self.game_mode == "ai_vs_ai" and
            not self.board.is_game_over()):
            self.root.after(1000, self.make_ai_move)  # 1 second delay between AI moves

    def check_game_status(self):
        """Check if the game is over and update status."""
        if self.board.is_game_over():
            self.game_running = False
            result = self.board.result()

            if result == "1-0":
                self.update_status("Game Over! White wins!")
                messagebox.showinfo("Game Over", "White wins!")
            elif result == "0-1":
                self.update_status("Game Over! Black wins!")
                messagebox.showinfo("Game Over", "Black wins!")
            else:
                self.update_status("Game Over! It's a draw!")
                messagebox.showinfo("Game Over", "It's a draw!")

    def update_status(self, message):
        """Update the status text."""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def on_mode_change(self):
        """Handle game mode change."""
        self.game_mode = self.mode_var.get()
        self.update_status(f"Game mode changed to: {self.game_mode}")

        # Start AI vs AI if selected
        if (self.game_mode == "ai_vs_ai" and
            self.game_running and
            not self.board.is_game_over()):
            self.root.after(1000, self.make_ai_move)

    def on_difficulty_change(self, value):
        """Handle AI difficulty change."""
        self.ai_depth = int(value)
        self.update_status(f"AI difficulty set to: {self.ai_depth}")

    def on_color_change(self):
        """Handle human color change."""
        self.human_color = chess.WHITE if self.color_var.get() == "white" else chess.BLACK
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        self.update_status(f"You are playing as: {color_name}")

        # If it's now AI's turn, make AI move
        if (self.game_mode == "human_vs_ai" and
            self.board.turn != self.human_color and
            self.game_running and
            not self.board.is_game_over()):
            self.root.after(500, self.make_ai_move)

    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.selected_square = None
        self.highlighted_squares = set()
        self.game_running = True
        self.ai_thinking = False
        self.last_move = None

        self.update_board()
        self.update_status("New game started! White to move.")

        # If AI should move first
        if (self.game_mode == "human_vs_ai" and
            self.human_color == chess.BLACK):
            self.root.after(1000, self.make_ai_move)
        elif self.game_mode == "ai_vs_ai":
            self.root.after(1000, self.make_ai_move)

    def undo_move(self):
        """Undo the last move."""
        if len(self.board.move_stack) > 0:
            move = self.board.pop()
            self.last_move = None
            self.selected_square = None
            self.update_board()
            self.update_status("Move undone.")
        else:
            self.update_status("No moves to undo.")

    def on_mouse_motion(self, event):
        """Handle mouse motion for hover effects."""
        # This could be used for hover effects in the future
        pass


def main():
    """Main function to run the chess GUI."""
    root = tk.Tk()

    # Set window icon and properties
    root.resizable(True, True)
    root.minsize(800, 600)

    # Create and run the chess game
    chess_gui = ChessGUI(root)

    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    main()
