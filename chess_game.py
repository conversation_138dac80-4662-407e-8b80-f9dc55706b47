#!/usr/bin/env python3
"""
Chess Game Implementation using python-chess library
Features:
- Alpha-beta minimax algorithm for AI moves
- Board evaluation function
- Complete game loop with both players
- Move validation and game state management
"""

import chess
import random
import time


def evaluate_board(board):
    """
    Evaluation function that calculates a score for the current board position
    based on material balance and piece positions.
    
    Returns:
        int: Positive score favors white, negative favors black
    """
    # Piece values for material calculation
    piece_values = {
        chess.PAWN: 1, 
        chess.KNIGHT: 3, 
        chess.BISHOP: 3, 
        chess.ROOK: 5, 
        chess.QUEEN: 9, 
        chess.KING: 0
    }
    
    score = 0
    
    # Calculate material balance
    for square, piece in board.piece_map().items():
        value = piece_values[piece.piece_type]
        if piece.color == chess.WHITE:
            score += value
        else:
            score -= value
    
    # Add bonuses/penalties based on piece positions and mobility
    for square, piece in board.piece_map().items():
        if piece.color == chess.WHITE:
            if piece.piece_type == chess.PAWN:
                score += 10 + (7 - chess.square_distance(square, chess.E2))
            elif piece.piece_type == chess.KNIGHT:
                score += 30 + len(board.attacks(square))
            elif piece.piece_type == chess.BISHOP:
                score += 30 + len(board.attacks(square))
            elif piece.piece_type == chess.ROOK:
                score += 50 + len(board.attacks(square))
            elif piece.piece_type == chess.QUEEN:
                score += 90 + len(board.attacks(square))
            elif piece.piece_type == chess.KING:
                score += 900 + len(board.attacks(square))
        else:
            if piece.piece_type == chess.PAWN:
                score -= 10 + (chess.square_distance(square, chess.E7))
            elif piece.piece_type == chess.KNIGHT:
                score -= 30 + len(board.attacks(square))
            elif piece.piece_type == chess.BISHOP:
                score -= 30 + len(board.attacks(square))
            elif piece.piece_type == chess.ROOK:
                score -= 50 + len(board.attacks(square))
            elif piece.piece_type == chess.QUEEN:
                score -= 90 + len(board.attacks(square))
            elif piece.piece_type == chess.KING:
                score -= 900 + len(board.attacks(square))
    
    return score


def alpha_betaMinMaxL(board, depth, alpha, beta, maximizing_player=True):
    """
    Alpha-beta minimax algorithm for move evaluation.
    
    Args:
        board: Current chess board state
        depth: Search depth remaining
        alpha: Alpha value for pruning
        beta: Beta value for pruning
        maximizing_player: True for white (max), False for black (min)
    
    Returns:
        int: Evaluation score for the position
    """
    # Base case: if depth is 0 or game is over
    if depth == 0 or board.is_game_over():
        return evaluate_board(board)
    
    if maximizing_player:
        max_eval = float('-inf')
        for move in board.legal_moves:
            board.push(move)
            eval_score = alpha_betaMinMaxL(board, depth - 1, alpha, beta, False)
            board.pop()
            max_eval = max(max_eval, eval_score)
            alpha = max(alpha, eval_score)
            if beta <= alpha:
                break  # Alpha-beta pruning
        return max_eval
    else:
        min_eval = float('inf')
        for move in board.legal_moves:
            board.push(move)
            eval_score = alpha_betaMinMaxL(board, depth - 1, alpha, beta, True)
            board.pop()
            min_eval = min(min_eval, eval_score)
            beta = min(beta, eval_score)
            if beta <= alpha:
                break  # Alpha-beta pruning
        return min_eval


def get_best_move(board, depth=2):
    """
    Get the best move for the current player using alpha-beta minimax.
    
    Args:
        board: Current chess board state
        depth: Search depth for the algorithm
    
    Returns:
        chess.Move: Best move found
    """
    legal_moves = list(board.legal_moves)
    if not legal_moves:
        return None
    
    best_move = None
    alpha = float('-inf')
    beta = float('inf')
    
    if board.turn == chess.WHITE:
        best_score = float('-inf')
        for move in legal_moves:
            board.push(move)
            score = alpha_betaMinMaxL(board, depth, alpha, beta, False)
            board.pop()
            if score > best_score:
                best_move = move
                best_score = score
    else:
        best_score = float('inf')
        for move in legal_moves:
            board.push(move)
            score = alpha_betaMinMaxL(board, depth, alpha, beta, True)
            board.pop()
            if score < best_score:
                best_move = move
                best_score = score
    
    return best_move


def print_game_info(board, move_count):
    """Print current game information."""
    print(f"\n--- Move {move_count} ---")
    print(f"Current player: {'White' if board.turn == chess.WHITE else 'Black'}")
    print(f"Board evaluation: {evaluate_board(board)}")
    print(board)
    print()


def play_chess_game(depth=2, show_moves=True):
    """
    Play a complete chess game between two AI players.
    
    Args:
        depth: Search depth for the minimax algorithm
        show_moves: Whether to display each move
    
    Returns:
        tuple: (result, move_count, execution_time)
    """
    print("Starting Chess Game with Alpha-Beta Minimax AI")
    print(f"Search depth: {depth}")
    print("=" * 50)
    
    # Initialize game
    board = chess.Board()
    board.turn = chess.WHITE  # White starts (default)
    move_count = 0
    start_time = time.time()
    
    # Game loop
    while not board.is_game_over():
        if show_moves:
            print_game_info(board, move_count + 1)
        
        # Get best move for current player
        best_move = get_best_move(board, depth)
        
        if best_move is None:
            print("No legal moves available!")
            break
        
        # Make the move
        if show_moves:
            print(f"{'White' if board.turn == chess.WHITE else 'Black'} plays: {best_move}")
        
        board.push(best_move)
        move_count += 1
        
        # Optional: Add a small delay to see moves in real-time
        if show_moves:
            time.sleep(0.5)
    
    # Game finished
    execution_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("GAME OVER!")
    print("=" * 50)
    print("Final board position:")
    print(board)
    print(f"\nResult: {board.result()}")
    print(f"Total moves: {move_count}")
    print(f"Execution time: {round(execution_time, 2)} seconds")
    
    # Determine winner
    result = board.result()
    if result == "1-0":
        print("White wins!")
    elif result == "0-1":
        print("Black wins!")
    else:
        print("Game ended in a draw!")
    
    return result, move_count, execution_time


if __name__ == "__main__":
    # Play a game
    play_chess_game(depth=2, show_moves=True)
