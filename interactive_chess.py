#!/usr/bin/env python3
"""
Interactive Chess Game - Play against the AI or watch AI vs AI
"""

import chess
import chess.engine
from chess_game import evaluate_board, alpha_betaMinMaxL, get_best_move
import time


def print_board_with_coordinates(board):
    """Print the board with file and rank coordinates."""
    print("  a b c d e f g h")
    lines = str(board).split('\n')
    for i, line in enumerate(lines):
        print(f"{8-i} {line} {8-i}")
    print("  a b c d e f g h")


def get_human_move(board):
    """Get a move from human player input."""
    while True:
        try:
            move_str = input("\nEnter your move (e.g., 'e2e4' or 'quit'): ").strip()
            
            if move_str.lower() == 'quit':
                return None
            
            # Try to parse the move
            move = chess.Move.from_uci(move_str)
            
            # Check if move is legal
            if move in board.legal_moves:
                return move
            else:
                print("Illegal move! Try again.")
                print("Legal moves:", [str(move) for move in list(board.legal_moves)[:10]], "...")
                
        except ValueError:
            print("Invalid move format! Use format like 'e2e4' or 'g1f3'")


def play_human_vs_ai(human_color=chess.WHITE, ai_depth=3):
    """
    Play a game between human and AI.
    
    Args:
        human_color: chess.WHITE or chess.BLACK
        ai_depth: Search depth for AI
    """
    print(f"\nStarting Human vs AI game!")
    print(f"You are playing as {'White' if human_color == chess.WHITE else 'Black'}")
    print(f"AI search depth: {ai_depth}")
    print("Enter moves in UCI format (e.g., 'e2e4', 'g1f3')")
    print("Type 'quit' to exit the game")
    print("=" * 50)
    
    board = chess.Board()
    move_count = 0
    
    while not board.is_game_over():
        move_count += 1
        print(f"\n--- Move {move_count} ---")
        print_board_with_coordinates(board)
        print(f"Board evaluation: {evaluate_board(board)}")
        
        if board.turn == human_color:
            # Human's turn
            print(f"\nYour turn ({'White' if board.turn == chess.WHITE else 'Black'})")
            move = get_human_move(board)
            if move is None:  # Player quit
                print("Game ended by player.")
                return
        else:
            # AI's turn
            print(f"\nAI thinking ({'White' if board.turn == chess.WHITE else 'Black'})...")
            start_time = time.time()
            move = get_best_move(board, ai_depth)
            think_time = time.time() - start_time
            print(f"AI plays: {move} (thought for {think_time:.2f}s)")
        
        # Make the move
        board.push(move)
    
    # Game over
    print("\n" + "=" * 50)
    print("GAME OVER!")
    print_board_with_coordinates(board)
    print(f"Result: {board.result()}")
    print(f"Total moves: {move_count}")
    
    result = board.result()
    if result == "1-0":
        winner = "White"
    elif result == "0-1":
        winner = "Black"
    else:
        winner = "Draw"
    
    print(f"Winner: {winner}")


def demo_random_vs_ai():
    """Demo game: Random player vs AI."""
    print("\nDemo: Random Player vs AI")
    print("=" * 30)
    
    board = chess.Board()
    move_count = 0
    
    while not board.is_game_over() and move_count < 50:  # Limit moves for demo
        move_count += 1
        print(f"\nMove {move_count} - {'White' if board.turn == chess.WHITE else 'Black'}")
        
        if board.turn == chess.WHITE:
            # Random player (White)
            legal_moves = list(board.legal_moves)
            move = legal_moves[random.randint(0, len(legal_moves) - 1)]
            print(f"Random plays: {move}")
        else:
            # AI player (Black)
            move = get_best_move(board, depth=2)
            print(f"AI plays: {move}")
        
        board.push(move)
        
        if move_count % 5 == 0:  # Show board every 5 moves
            print(board)
            time.sleep(1)
    
    print(f"\nFinal position after {move_count} moves:")
    print(board)
    print(f"Game status: {board.result() if board.is_game_over() else 'In progress'}")


def main_menu():
    """Main menu for the chess game."""
    while True:
        print("\n" + "=" * 50)
        print("CHESS GAME MENU")
        print("=" * 50)
        print("1. Play as White against AI")
        print("2. Play as Black against AI")
        print("3. Watch AI vs AI game")
        print("4. Demo: Random vs AI")
        print("5. Quick AI vs AI (no display)")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == '1':
            play_human_vs_ai(human_color=chess.WHITE, ai_depth=3)
        elif choice == '2':
            play_human_vs_ai(human_color=chess.BLACK, ai_depth=3)
        elif choice == '3':
            from chess_game import play_chess_game
            play_chess_game(depth=2, show_moves=True)
        elif choice == '4':
            demo_random_vs_ai()
        elif choice == '5':
            from chess_game import play_chess_game
            result, moves, time_taken = play_chess_game(depth=2, show_moves=False)
            print(f"Quick game result: {result} in {moves} moves ({time_taken:.2f}s)")
        elif choice == '6':
            print("Thanks for playing!")
            break
        else:
            print("Invalid choice! Please enter 1-6.")


if __name__ == "__main__":
    import random
    main_menu()
