#!/usr/bin/env python3
"""
Enhanced Chess Game GUI with additional features
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import chess.pgn
from chess_gui import ChessGUI
from chess_game import evaluate_board, get_best_move
import threading
import time
import io


class EnhancedChessGUI(ChessGUI):
    def __init__(self, root):
        super().__init__(root)
        self.move_history = []
        self.setup_enhanced_features()
    
    def setup_enhanced_features(self):
        """Add enhanced features to the GUI."""
        # Add menu bar
        self.setup_menu()
        
        # Add move history panel
        self.setup_move_history()
        
        # Add analysis features
        self.setup_analysis()
    
    def setup_menu(self):
        """Create menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Game", command=self.new_game)
        file_menu.add_separator()
        file_menu.add_command(label="Save Game", command=self.save_game)
        file_menu.add_command(label="Load Game", command=self.load_game)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Game menu
        game_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Game", menu=game_menu)
        game_menu.add_command(label="Flip Board", command=self.flip_board)
        game_menu.add_command(label="Show Legal Moves", command=self.toggle_legal_moves)
        game_menu.add_separator()
        game_menu.add_command(label="Analyze Position", command=self.analyze_position)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="How to Play", command=self.show_help)
        help_menu.add_command(label="About", command=self.show_about)
    
    def setup_move_history(self):
        """Add move history display."""
        # Create a new frame for move history
        history_frame = tk.LabelFrame(self.control_frame, text="Move History", 
                                     font=('Arial', 12, 'bold'), 
                                     bg='#34495e', fg='white')
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create treeview for move history
        columns = ('Move', 'White', 'Black')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=6)
        
        # Configure columns
        self.history_tree.heading('Move', text='#')
        self.history_tree.heading('White', text='White')
        self.history_tree.heading('Black', text='Black')
        
        self.history_tree.column('Move', width=40)
        self.history_tree.column('White', width=80)
        self.history_tree.column('Black', width=80)
        
        # Add scrollbar
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        # Pack widgets
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def setup_analysis(self):
        """Add analysis features."""
        analysis_frame = tk.LabelFrame(self.control_frame, text="Analysis", 
                                      font=('Arial', 12, 'bold'), 
                                      bg='#34495e', fg='white')
        analysis_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Analysis buttons
        button_style = {'bg': '#e74c3c', 'fg': 'white', 'font': ('Arial', 9, 'bold'),
                       'relief': tk.RAISED, 'bd': 2, 'padx': 5, 'pady': 3}
        
        analyze_btn = tk.Button(analysis_frame, text="Analyze", 
                               command=self.analyze_position, **button_style)
        analyze_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        hint_btn = tk.Button(analysis_frame, text="Hint", 
                            command=self.show_hint, **button_style)
        hint_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Analysis display
        self.analysis_var = tk.StringVar(value="Click 'Analyze' for position evaluation")
        analysis_label = tk.Label(analysis_frame, textvariable=self.analysis_var,
                                 bg='#2c3e50', fg='white', font=('Arial', 9),
                                 wraplength=200, justify=tk.LEFT)
        analysis_label.pack(fill=tk.X, padx=5, pady=5)
    
    def make_move(self, move):
        """Override to add move to history."""
        # Get move notation before making the move
        move_san = self.board.san(move)
        
        # Call parent method
        super().make_move(move)
        
        # Add to move history
        self.add_move_to_history(move_san)
    
    def add_move_to_history(self, move_san):
        """Add a move to the history display."""
        self.move_history.append(move_san)
        
        # Update treeview
        move_number = (len(self.move_history) + 1) // 2
        
        if len(self.move_history) % 2 == 1:  # White move
            # Insert new row
            item_id = self.history_tree.insert('', 'end', values=(move_number, move_san, ''))
        else:  # Black move
            # Update last row
            items = self.history_tree.get_children()
            if items:
                last_item = items[-1]
                values = self.history_tree.item(last_item)['values']
                self.history_tree.item(last_item, values=(values[0], values[1], move_san))
        
        # Scroll to bottom
        self.history_tree.see(self.history_tree.get_children()[-1])
    
    def new_game(self):
        """Override to clear move history."""
        super().new_game()
        self.move_history = []
        
        # Clear history display
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
    
    def flip_board(self):
        """Flip the board view."""
        # This is a placeholder - would require redrawing the board
        self.update_status("Board flip feature coming soon!")
    
    def toggle_legal_moves(self):
        """Toggle showing legal moves."""
        self.update_status("Legal moves toggle feature coming soon!")
    
    def analyze_position(self):
        """Analyze the current position."""
        if self.ai_thinking:
            return
        
        self.analysis_var.set("Analyzing...")
        
        def analysis_thread():
            try:
                # Get evaluation at different depths
                eval_depth_1 = evaluate_board(self.board)
                
                # Get best move
                best_move = get_best_move(self.board, depth=2)
                best_move_san = self.board.san(best_move) if best_move else "None"
                
                # Count material
                white_material = sum(len(self.board.pieces(piece_type, chess.WHITE)) * value 
                                   for piece_type, value in [(chess.PAWN, 1), (chess.KNIGHT, 3), 
                                                            (chess.BISHOP, 3), (chess.ROOK, 5), 
                                                            (chess.QUEEN, 9)].items())
                black_material = sum(len(self.board.pieces(piece_type, chess.BLACK)) * value 
                                   for piece_type, value in [(chess.PAWN, 1), (chess.KNIGHT, 3), 
                                                            (chess.BISHOP, 3), (chess.ROOK, 5), 
                                                            (chess.QUEEN, 9)].items())
                
                analysis_text = f"Evaluation: {eval_depth_1:+d}\n"
                analysis_text += f"Material: W:{white_material} B:{black_material}\n"
                analysis_text += f"Best move: {best_move_san}"
                
                self.root.after(0, lambda: self.analysis_var.set(analysis_text))
                
            except Exception as e:
                self.root.after(0, lambda: self.analysis_var.set(f"Analysis error: {str(e)}"))
        
        threading.Thread(target=analysis_thread, daemon=True).start()
    
    def show_hint(self):
        """Show a hint for the current position."""
        if self.ai_thinking:
            return
        
        def hint_thread():
            try:
                best_move = get_best_move(self.board, depth=2)
                if best_move:
                    hint_text = f"Hint: Consider {self.board.san(best_move)}"
                    self.root.after(0, lambda: self.update_status(hint_text))
                else:
                    self.root.after(0, lambda: self.update_status("No good moves found!"))
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"Hint error: {str(e)}"))
        
        threading.Thread(target=hint_thread, daemon=True).start()
    
    def save_game(self):
        """Save the current game to a PGN file."""
        if not self.move_history:
            messagebox.showwarning("No Game", "No moves to save!")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                game = chess.pgn.Game()
                game.headers["Event"] = "Chess GUI Game"
                game.headers["White"] = "Player/AI"
                game.headers["Black"] = "Player/AI"
                
                # Recreate the game from move history
                board = chess.Board()
                node = game
                
                for move_san in self.move_history:
                    move = board.parse_san(move_san)
                    node = node.add_variation(move)
                    board.push(move)
                
                with open(filename, 'w') as f:
                    print(game, file=f)
                
                self.update_status(f"Game saved to {filename}")
                
            except Exception as e:
                messagebox.showerror("Save Error", f"Could not save game: {str(e)}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        filename = filedialog.askopenfilename(
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    game = chess.pgn.read_game(f)
                
                if game:
                    # Start new game
                    self.new_game()
                    
                    # Play through the moves
                    board = chess.Board()
                    for move in game.mainline_moves():
                        if move in board.legal_moves:
                            move_san = board.san(move)
                            board.push(move)
                            self.board.push(move)
                            self.add_move_to_history(move_san)
                    
                    self.update_board()
                    self.update_status(f"Game loaded from {filename}")
                else:
                    messagebox.showerror("Load Error", "No valid game found in file!")
                    
            except Exception as e:
                messagebox.showerror("Load Error", f"Could not load game: {str(e)}")
    
    def show_help(self):
        """Show help dialog."""
        help_text = """
Chess Game Help

How to Play:
• Click on a piece to select it
• Click on a highlighted square to move
• The game follows standard chess rules

Game Modes:
• Human vs AI: Play against the computer
• AI vs AI: Watch two AIs play
• Human vs Human: Two players on same computer

Controls:
• New Game: Start a fresh game
• AI Move: Force AI to move (if it's AI's turn)
• Undo Move: Take back the last move
• Analyze: Get position evaluation
• Hint: Get a move suggestion

Features:
• Save/Load games in PGN format
• Move history display
• Position analysis
• Adjustable AI difficulty
        """
        messagebox.showinfo("Help", help_text)
    
    def show_about(self):
        """Show about dialog."""
        about_text = """
Chess Game with AI
Version 1.0

A complete chess implementation with:
• Alpha-beta minimax AI
• Beautiful graphical interface
• PGN game save/load
• Position analysis
• Multiple game modes

Built with Python and tkinter
Using python-chess library
        """
        messagebox.showinfo("About", about_text)


def main():
    """Main function to run the enhanced chess GUI."""
    root = tk.Tk()
    
    # Set window properties
    root.resizable(True, True)
    root.minsize(1000, 700)
    
    # Create and run the enhanced chess game
    chess_gui = EnhancedChessGUI(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    main()
