#!/usr/bin/env python3
"""
Test script to verify the GUI fix works correctly
"""

import chess
from chess_game import get_best_move

def test_move_notation_bug():
    """Test that we can get SAN notation correctly."""
    print("Testing move notation fix...")
    
    # Create a board
    board = chess.Board()
    
    # Get a move
    move = chess.Move.from_uci("e2e4")
    
    # This should work - get SAN before making the move
    try:
        move_san = board.san(move)
        print(f"✅ SAN before move: {move_san}")
        
        # Make the move
        board.push(move)
        print(f"✅ Move made successfully")
        
        # Get another move
        move2 = chess.Move.from_uci("e7e5")
        move2_san = board.san(move2)
        print(f"✅ Second move SAN: {move2_san}")
        
        board.push(move2)
        print(f"✅ Second move made successfully")
        
        print("\n✅ All tests passed! The GUI fix should work correctly.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def test_ai_move():
    """Test AI move generation."""
    print("\nTesting AI move generation...")
    
    try:
        board = chess.Board()
        best_move = get_best_move(board, depth=1)
        
        if best_move:
            move_san = board.san(best_move)
            print(f"✅ AI suggested move: {move_san}")
            return True
        else:
            print("❌ AI couldn't find a move")
            return False
            
    except Exception as e:
        print(f"❌ AI error: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("GUI FIX VERIFICATION")
    print("=" * 50)
    
    success = True
    
    # Test 1: Move notation
    success &= test_move_notation_bug()
    
    # Test 2: AI moves
    success &= test_ai_move()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("The GUI should now work without errors.")
        print("\nYou can safely run:")
        print("• python chess_gui.py")
        print("• python chess_gui_enhanced.py")
    else:
        print("❌ SOME TESTS FAILED!")
        print("There may still be issues with the GUI.")
    print("=" * 50)

if __name__ == "__main__":
    main()
