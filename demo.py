#!/usr/bin/env python3
"""
Demo script to showcase all chess game features
"""

import sys
import time
from chess_game import play_chess_game, evaluate_board
import chess

def demo_console_game():
    """Demo the console-based chess game."""
    print("=" * 60)
    print("DEMO: Console Chess Game (AI vs AI)")
    print("=" * 60)
    
    print("Running a quick AI vs AI game with depth 2...")
    result, moves, exec_time = play_chess_game(depth=2, show_moves=False)
    
    print(f"✅ Game completed!")
    print(f"   Result: {result}")
    print(f"   Total moves: {moves}")
    print(f"   Execution time: {exec_time:.2f} seconds")
    print()

def demo_evaluation():
    """Demo the board evaluation function."""
    print("=" * 60)
    print("DEMO: Board Evaluation Function")
    print("=" * 60)
    
    # Starting position
    board = chess.Board()
    print("Starting position:")
    print(board)
    print(f"Evaluation: {evaluate_board(board)}")
    print()
    
    # After some moves
    moves = ["e2e4", "e7e5", "g1f3", "b8c6", "f1b5"]
    print("After opening moves (<PERSON><PERSON>):")
    for move_str in moves:
        move = chess.Move.from_uci(move_str)
        board.push(move)
    
    print(board)
    print(f"Evaluation: {evaluate_board(board)}")
    print()

def demo_gui_info():
    """Show information about the GUI."""
    print("=" * 60)
    print("DEMO: Graphical User Interface")
    print("=" * 60)
    
    print("🎮 To run the beautiful chess GUI:")
    print("   python chess_gui.py")
    print()
    print("🚀 Enhanced GUI with extra features:")
    print("   python chess_gui_enhanced.py")
    print()
    print("📋 GUI Features:")
    print("   ✓ Beautiful visual chess board")
    print("   ✓ Interactive piece movement")
    print("   ✓ Real-time move highlighting")
    print("   ✓ Multiple game modes")
    print("   ✓ AI difficulty settings")
    print("   ✓ Move history display")
    print("   ✓ Position analysis")
    print("   ✓ Save/Load games (Enhanced version)")
    print()

def demo_interactive():
    """Demo interactive features."""
    print("=" * 60)
    print("DEMO: Interactive Features")
    print("=" * 60)
    
    print("🎯 Available game modes:")
    print("   1. Human vs AI - Play against the computer")
    print("   2. AI vs AI - Watch two AIs compete")
    print("   3. Human vs Human - Two players on same computer")
    print()
    
    print("🧠 AI Difficulty Levels:")
    print("   Level 1: Very fast, basic moves (~0.01s per move)")
    print("   Level 2: Good balance (~0.1s per move)")
    print("   Level 3: Strong play (~1s per move)")
    print("   Level 4: Very strong (~5s per move)")
    print("   Level 5: Expert level (~20s per move)")
    print()

def main():
    """Run all demos."""
    print("🏁 CHESS GAME DEMONSTRATION")
    print("Welcome to the complete chess game with AI!")
    print()
    
    try:
        # Demo 1: Console game
        demo_console_game()
        time.sleep(1)
        
        # Demo 2: Evaluation function
        demo_evaluation()
        time.sleep(1)
        
        # Demo 3: Interactive info
        demo_interactive()
        time.sleep(1)
        
        # Demo 4: GUI info
        demo_gui_info()
        
        print("=" * 60)
        print("🎉 DEMO COMPLETE!")
        print("=" * 60)
        print()
        print("Ready to play? Choose your preferred interface:")
        print("• GUI (Recommended): python chess_gui.py")
        print("• Enhanced GUI: python chess_gui_enhanced.py")
        print("• Console Menu: python interactive_chess.py")
        print("• Quick AI Game: python chess_game.py")
        print()
        print("Have fun playing chess! 🏆")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install chess")

if __name__ == "__main__":
    main()
