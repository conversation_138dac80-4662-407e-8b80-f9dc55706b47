# Chess Game with Alpha-Beta Minimax AI

A complete chess game implementation in Python using the `python-chess` library with an AI player powered by the alpha-beta minimax algorithm.

## Features

- **Complete Chess Implementation**: Full chess rules, move validation, and game state management
- **Alpha-Beta Minimax AI**: Intelligent AI player with configurable search depth
- **Board Evaluation**: Sophisticated position evaluation considering material and piece mobility
- **Multiple Game Modes**:
  - Human vs AI
  - AI vs AI (watch mode)
  - Interactive gameplay
  - Quick games for testing

## Files

- `chess_game.py` - Core chess game implementation with AI
- `chess_gui.py` - **Graphical User Interface** - Beautiful GUI for playing chess
- `chess_gui_enhanced.py` - Enhanced GUI with additional features (save/load, analysis)
- `interactive_chess.py` - Interactive menu and human vs AI gameplay
- `test_chess.py` - Test suite to verify functionality
- `run_chess_gui.py` - Simple launcher for the GUI
- `requirements.txt` - Python dependencies

## Installation

1. Install the required dependency:

```bash
pip install -r requirements.txt
```

Or install directly:

```bash
pip install chess
```

## Usage

### 🎮 **Graphical Interface (Recommended)**

```bash
python chess_gui.py
```

Or use the launcher:

```bash
python run_chess_gui.py
```

### Enhanced GUI with Extra Features

```bash
python chess_gui_enhanced.py
```

### Command Line Options

#### Quick Start - AI vs AI Game

```bash
python chess_game.py
```

#### Interactive Menu

```bash
python interactive_chess.py
```

#### Run Tests

```bash
python test_chess.py
```

## 🎨 GUI Features

### Main Features

- **Beautiful Visual Board**: 8x8 chess board with proper colors and coordinates
- **Interactive Piece Movement**: Click to select pieces and make moves
- **Real-time Highlighting**: Shows selected pieces and legal moves
- **Multiple Game Modes**: Human vs AI, AI vs AI, Human vs Human
- **AI Difficulty Settings**: Adjustable from 1-5 levels
- **Game Status Display**: Live updates on game state and evaluation
- **Move History**: Complete record of all moves played

### Enhanced GUI Additional Features

- **Menu System**: File operations, game controls, and help
- **Save/Load Games**: PGN format support for game persistence
- **Position Analysis**: Deep evaluation of current position
- **Move Hints**: AI suggestions for your next move
- **Material Count**: Track piece values for both sides

### Controls

- **Left Click**: Select piece or make move
- **New Game Button**: Start fresh game
- **AI Move Button**: Force AI to move
- **Undo Button**: Take back last move
- **Difficulty Slider**: Adjust AI strength
- **Color Selection**: Choose to play as White or Black

## Game Modes

### 1. AI vs AI

Watch two AI players compete against each other:

```python
from chess_game import play_chess_game
play_chess_game(depth=2, show_moves=True)
```

### 2. Human vs AI

Play against the AI:

```python
from interactive_chess import play_human_vs_ai
import chess

# Play as White
play_human_vs_ai(human_color=chess.WHITE, ai_depth=3)

# Play as Black
play_human_vs_ai(human_color=chess.BLACK, ai_depth=3)
```

### 3. Custom Game

```python
import chess
from chess_game import get_best_move, evaluate_board

board = chess.Board()
best_move = get_best_move(board, depth=3)
score = evaluate_board(board)
```

## Algorithm Details

### Board Evaluation Function

The `evaluate_board()` function calculates position scores based on:

- **Material Balance**: Standard piece values (Pawn=1, Knight=3, Bishop=3, Rook=5, Queen=9)
- **Piece Mobility**: Bonus points for pieces that can attack more squares
- **Positional Factors**: Pawn advancement, piece development

### Alpha-Beta Minimax

The `alpha_betaMinMaxL()` function implements:

- **Minimax Algorithm**: Recursive game tree search
- **Alpha-Beta Pruning**: Optimization to reduce search space
- **Configurable Depth**: Adjustable search depth for performance vs strength

## Configuration

### AI Strength

Adjust the search depth to change AI difficulty:

- `depth=1`: Very fast, basic moves
- `depth=2`: Good balance of speed and intelligence
- `depth=3`: Strong play, slower thinking
- `depth=4+`: Very strong but much slower

### Move Input Format

When playing as human, enter moves in UCI format:

- `e2e4` - Move pawn from e2 to e4
- `g1f3` - Move knight from g1 to f3
- `e1g1` - Castle kingside (as king move)

## Example Output

```
Starting Chess Game with Alpha-Beta Minimax AI
Search depth: 2
==================================================

--- Move 1 ---
Current player: White
Board evaluation: 0
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

White plays: e2e4

--- Move 2 ---
Current player: Black
Board evaluation: 24
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . P . . .
. . . . . . . .
P P P P . P P P
R N B Q K B N R

Black plays: e7e5
...
```

## Testing

The test suite verifies:

- Basic chess functionality
- Move generation and validation
- Board evaluation accuracy
- Alpha-beta algorithm performance
- Complete game execution

Run tests with:

```bash
python test_chess.py
```

## Performance

Typical performance on modern hardware:

- Depth 1: ~0.001s per move
- Depth 2: ~0.01s per move
- Depth 3: ~0.1s per move
- Depth 4: ~1s per move

## License

This project is open source and available under the MIT License.
