#!/usr/bin/env python3
"""
Simple launcher for the Chess GUI
"""

import sys
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required dependencies are available."""
    try:
        import chess
        return True
    except ImportError:
        return False

def main():
    """Main launcher function."""
    # Check dependencies
    if not check_dependencies():
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        messagebox.showerror(
            "Missing Dependencies", 
            "The 'chess' library is required.\n\n"
            "Please install it with:\n"
            "pip install chess\n\n"
            "Then run this program again."
        )
        return
    
    # Try to import and run the GUI
    try:
        from chess_gui import main as run_basic_gui
        run_basic_gui()
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Error", 
            f"Failed to start Chess GUI:\n{str(e)}\n\n"
            "Please check that all files are present and try again."
        )

if __name__ == "__main__":
    main()
