#!/usr/bin/env python3
"""
Test script for the chess game implementation
"""

import chess
from chess_game import evaluate_board, alpha_betaMinMaxL, get_best_move, play_chess_game
import time


def test_basic_functionality():
    """Test basic chess functionality."""
    print("Testing basic chess functionality...")
    
    # Test 1: Create a board
    board = chess.Board()
    print(f"✓ Board created: {board.turn == chess.WHITE}")
    
    # Test 2: Get legal moves
    legal_moves = list(board.legal_moves)
    print(f"✓ Legal moves found: {len(legal_moves)} moves")
    
    # Test 3: Test evaluation function
    score = evaluate_board(board)
    print(f"✓ Board evaluation: {score}")
    
    # Test 4: Make a move
    move = legal_moves[0]
    board.push(move)
    print(f"✓ Move made: {move}")
    
    # Test 5: Test alpha-beta function
    alpha_beta_score = alpha_betaMinMaxL(board, 2, float('-inf'), float('inf'), True)
    print(f"✓ Alpha-beta evaluation: {alpha_beta_score}")
    
    print("All basic tests passed!\n")


def test_game_scenarios():
    """Test different game scenarios."""
    print("Testing game scenarios...")
    
    # Test 1: Starting position evaluation
    board = chess.Board()
    start_eval = evaluate_board(board)
    print(f"✓ Starting position evaluation: {start_eval}")
    
    # Test 2: After a few moves
    moves = ["e2e4", "e7e5", "g1f3", "b8c6"]
    for move_str in moves:
        move = chess.Move.from_uci(move_str)
        board.push(move)
    
    mid_eval = evaluate_board(board)
    print(f"✓ After opening moves evaluation: {mid_eval}")
    
    # Test 3: Get best move
    best_move = get_best_move(board, depth=2)
    print(f"✓ Best move found: {best_move}")
    
    print("Game scenario tests passed!\n")


def test_performance():
    """Test performance of the algorithm."""
    print("Testing performance...")
    
    board = chess.Board()
    
    # Test different depths
    for depth in [1, 2, 3]:
        start_time = time.time()
        best_move = get_best_move(board, depth=depth)
        end_time = time.time()
        
        print(f"✓ Depth {depth}: {best_move} in {end_time - start_time:.3f}s")
    
    print("Performance tests completed!\n")


def run_quick_game():
    """Run a quick game to test the full system."""
    print("Running a quick game test...")
    
    result, moves, exec_time = play_chess_game(depth=1, show_moves=False)
    print(f"✓ Quick game completed: {result} in {moves} moves ({exec_time:.2f}s)")
    
    print("Quick game test passed!\n")


def main():
    """Run all tests."""
    print("=" * 50)
    print("CHESS GAME TEST SUITE")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_game_scenarios()
        test_performance()
        run_quick_game()
        
        print("=" * 50)
        print("ALL TESTS PASSED! ✓")
        print("The chess game is ready to play!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
